/* eslint-disable */
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import Book from './App.vue'
// import router from './router'
import store from './store'
import i18n from './i18n'
import 'element-plus/dist/index.css';
// dayjs.locale(i18n.global.locale=='zh'?'zh-cn':'en');
(window as any).changeVueLang = (lang: string) => {
  i18n.global.locale = lang
  // dayjs.locale(lang=='zh'?'zh-cn':'en');
}
// 导出创建函数
export function createAppInstance () {
  const book = createApp(Book)
  book.use(ElementPlus).use(store).use(i18n)
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    book.component(key, component)
  }
  book.mount('#book-app')
  return book
}

(window as any).bookInstance = createAppInstance

// 本地开发时自动创建Vue实例，在测试/生产不能自动创建。请删除下面一行代码
createAppInstance()
