{"name": "book-instruments", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "2.1.0", "dayjs": "^1.11.13", "element-plus": "2.9.1", "vue": "^3.2.13", "vue-i18n": "^11.1.3", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@types/node": "^18.8.0", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.32.7", "sass-loader": "^12.0.0", "tslib": "^2.8.1", "typescript": "^5.0.0", "webpack-manifest-plugin": "^5.0.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "@vue/standard", "@vue/typescript/recommended"], "parserOptions": {"ecmaVersion": 2020}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}