export interface BookingItem {
  [key: string]: any
  experimenter: string;
  instrument: string;
  bookedTime: string[];
  reminder: string;
  ELNPage: string[];
  remark: string;
}
export interface BookDetailsTable {
  [key: string]: any
  instrumentName: string
  instrumentCode: string
  state: string
  responsiblePerson: string
  position: string
  bookableTime: string[]
  bookedInfo?: BookingItem[]
  singleTimeLimit: string
  advance: string
  whiteMap?:Map<string, number>
  bookedMap?:Map<string, number>
}
