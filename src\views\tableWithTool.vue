<template>
  <div class="table-container">
    <el-table :data="tableData" border>
      <el-table-column prop="date" label="日期" width="180">
        <template #default="scope">
          <el-tooltip
            :content="`日期详情: ${scope.row.date}`"
            placement="top"
            :show-after="500"
          >
            <div
              class="cell-content"
              @click="handleCellClick(scope.row, 'date', $event)"
            >
              {{ scope.row.date }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="name" label="姓名" width="180">
        <template #default="scope">
          <el-tooltip
            :content="`用户: ${scope.row.name}`"
            placement="top"
            :show-after="500"
          >
            <div
              class="cell-content"
              @click="handleCellClick(scope.row, 'name', $event)"
            >
              {{ scope.row.name }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="address" label="地址">
        <template #default="scope">
          <el-tooltip
            :content="`地址信息: ${scope.row.address}`"
            placement="top"
            :show-after="500"
          >
            <div
              class="cell-content"
              @click="handleCellClick(scope.row, 'address', $event)"
            >
              {{ scope.row.address }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-popover
      v-model:visible="popoverVisible"
      :width="300"
      trigger="click"
      transition="el-zoom-in-left"
      :show-arrow="false"
      :virtual-ref="popoverTarget"
      virtual-triggering
    >
      <div class="popover-content">
        <h4>{{ popoverTitle }}</h4>
        <p>{{ popoverContent }}</p>
        <div class="popover-actions">
          <el-button size="small" @click="closePopover">关闭</el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 表格数据
const tableData = ref([
  {
    date: '2023-05-03',
    name: '张三',
    address: '北京市朝阳区',
    dateDetails: '周三，晴天，适合外出活动',
    nameDetails: '张三，男，28岁，软件工程师，擅长前端开发',
    addressDetails: '北京市朝阳区某小区3号楼2单元，交通便利，环境优美'
  },
  {
    date: '2023-05-04',
    name: '李四',
    address: '上海市浦东新区',
    dateDetails: '周四，多云，温度适宜',
    nameDetails: '李四，女，32岁，UI/UX设计师，有丰富的设计经验',
    addressDetails: '上海市浦东新区某花园1期5栋，高档小区，配套设施完善'
  },
  {
    date: '2023-05-05',
    name: '王五',
    address: '广州市天河区',
    dateDetails: '周五，阴天，可能有小雨',
    nameDetails: '王五，男，26岁，产品经理，负责多个重要项目',
    addressDetails: '广州市天河区CBD核心区域，商业繁华，生活便利'
  }
])

// Popover相关状态
const popoverVisible = ref(false)
const popoverTarget = ref()
const popoverTitle = ref('')
const popoverContent = ref('')

// 处理单元格点击
const handleCellClick = (row: any, column: string, event: MouseEvent) => {
  // 阻止事件冒泡
  event.stopPropagation()

  // 设置popover内容
  if (column === 'date') {
    popoverTitle.value = '日期详细信息'
    popoverContent.value = row.dateDetails
  } else if (column === 'name') {
    popoverTitle.value = '人员详细信息'
    popoverContent.value = row.nameDetails
  } else if (column === 'address') {
    popoverTitle.value = '地址详细信息'
    popoverContent.value = row.addressDetails
  }

  // 设置虚拟触发元素
  popoverTarget.value = event.target

  // 显示popover
  popoverVisible.value = true
}

// 关闭popover
const closePopover = () => {
  popoverVisible.value = false
}

// 点击其他地方关闭popover
const handleDocumentClick = (event: MouseEvent) => {
  // 如果点击的不是popover内容，则关闭popover
  const target = event.target as HTMLElement
  if (!target.closest('.el-popover') && !target.closest('.cell-content')) {
    closePopover()
  }
}

  onMounted(() => {
    document.addEventListener('click', handleDocumentClick)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleDocumentClick)
  })
</script>

<style scoped>
.table-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.cell-content {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  user-select: none;
}

.cell-content:hover {
  background-color: #f5f7fa;
}

.cell-content:active {
  background-color: #e4e7ed;
}

.popover-content {
  padding: 12px;
}

.popover-content h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.popover-content p {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.5;
  font-size: 14px;
}

.popover-actions {
  text-align: right;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 8px 0;
}

:deep(.el-table .cell) {
  padding: 0 8px;
}
</style>
