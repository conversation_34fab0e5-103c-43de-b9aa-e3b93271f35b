<template>
  <div class="book-instruments">
    <ElConfigProvider :locale="currentElementLocale">
      <ElDialog
          v-model="dialogVisible"
          fullscreen
          top="40vh"
          width="70%"
          draggable
          :append-to-body="false"
          :show-close="false"
          @close="bookUnmounted"
      >
        <template #header="{ close }">
          <div class="my-book-header">
            <ElButton
                key="退出"
                text @click="close">
              <ElIcon>
                <CloseBold/>
              </ElIcon>
              &nbsp;退出
            </ElButton>
            <span class="book-title">
            仪器预约
          </span>
          </div>
        </template>
        <ElRow>
          <div class="left-header">
            <div class="time-type">
              <ElRadioGroup v-model="timeType" @change="handleBookTimeType">
                <ElRadioButton label="day">{{ $t('bookInstruments.day') }}</ElRadioButton>
                <ElRadioButton label="week">{{ $t('bookInstruments.week') }}</ElRadioButton>
              </ElRadioGroup>
            </div>
            <div class="custom-date-picker">
              <ElDatePicker
                  v-if="timeType==='day'"
                  v-model="bookTime"
                  :default-value="new Date()"
                  type="date"
                  :popper-class="'booking-header-date-picker'"
                  :shortcuts="shortcuts"
                  size="default"
                  :clearable="false"
                  :append-to-body="false"
                  :editable="false"
                  :format="t('bookInstruments.dateFormat')"
              />
              <div class="custom-week-picker">
                <ElDatePicker
                    v-if="timeType==='week'"
                    v-model="bookTime"
                    type="week"
                    :popper-class="'booking-header-date-picker'"
                    :first-day-of-week="1"
                    :clearable="false"
                    :editable="false"
                    :format="`${startDate} - ${endDate}`"
                    @change="handleWeekChange"
                />
              </div>
            </div>
            <div class="time-type quickTimePick">
              <ElIcon class="pointer-style" @click="reduceTime">
                <ArrowLeft/>
              </ElIcon>
              <ElIcon class="pointer-style" @click="increaseTime">
                <ArrowRight/>
              </ElIcon>
              <ElButton class="today-button" @click="currentTime">{{ t('bookInstruments.today') }}</ElButton>
            </div>

          </div>
          <div class="header-tail">
            <div class="booked-instruments">
              <ElSwitch
                  v-model="onlyBooked"
                  size="small"
                  :loading="bookedLoading"
                  :before-change="showBookedData"
              />
              <span>仅显示我预约过的</span>
            </div>
            <ElButton type="primary" class="create-book">
              <ElIcon>
                <Plus/>
              </ElIcon>&nbsp;创建预约
            </ElButton>
          </div>
        </ElRow>
        <ElRow>
          <booked-day-details-table v-if="timeType==='day'" :bookTime="bookTime" :onlyBooked="onlyBooked" ref="dayTableRef"></booked-day-details-table>
          <booked-week-details-table v-else :weekDate="weekDate" :onlyBooked="onlyBooked" ref="weekTableRef" @bookedLoadingChange="handleBookedChange"></booked-week-details-table>
        </ElRow>
      </ElDialog>
    </ElConfigProvider>
  </div>
</template>

<script setup lang="ts">
import {
  ElRadioGroup, ElConfigProvider, ElDialog,
  ElButton, ElIcon, ElRow, ElRadioButton, ElDatePicker, ElSwitch, dayjs
} from 'element-plus'
import { Plus, CloseBold, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import dayjsR from 'dayjs'
import 'dayjs/locale/zh-cn'
import isoWeek from 'dayjs/plugin/isoWeek'
import { computed, ref, defineProps } from 'vue'
import { useI18n } from 'vue-i18n'
import BookedDayDetailsTable from './bookedDayDetailsTable.vue'
import BookedWeekDetailsTable from './bookedWeekDetailsTable.vue'
import eleZH from 'element-plus/es/locale/lang/zh-cn'
import eleEN from 'element-plus/es/locale/lang/en'
// 动态切换 Element 语言包
const currentElementLocale = computed(() => {
  dayjs.en.weekStart = (window as any).lang === 'cn' ? 1 : 1
  return (window as any).lang === 'cn' ? eleZH : eleZH
})
dayjsR.extend(isoWeek)
dayjsR.locale((window as any).lang === 'cn' ? 'zh-cn' : 'en')

// 强制设置周一为每周第一天（ISO标准）

const { t } = useI18n()
const bookedLoading = ref(true)
const dialogVisible = ref<boolean>(true)
const timeType = ref<string>('day')
const bookTime = ref<Date>(new Date())
const startDate = ref('')
const endDate = ref('')
const onlyBooked = ref<boolean>(false)
const weekDate = ref([])
const dayTableRef = ref()
const weekTableRef = ref()
const props = defineProps({
  closeBookInstruments: {
    type: Function,
    required: true
  }
})
// 卸载vue实例托管到php页面执行
const bookUnmounted = () => {
  props.closeBookInstruments()
}

const handleBookTimeType = (val) => {
  timeType.value = val
  // (window as any).changeVueLang('en')
  if (val === 'week') {
    // 计算周一的日期（假设周一为周起始日）
    handleWeekChange(bookTime.value)
  }
}
const shortcuts = computed(() => [
  {
    text: t('bookInstruments.today'),
    value: new Date()
  },
  {
    text: t('bookInstruments.yesterday'),
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return date
    }
  },
  {
    text: t('bookInstruments.weekAgo'),
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      return date
    }
  }
])
const reduceTime = () => {
  if (timeType.value === 'day') {
    bookTime.value = new Date(bookTime.value.getTime() - 3600 * 1000 * 24)
  } else if (timeType.value === 'week') {
    handleWeekChange(new Date(bookTime.value.getTime() - 3600 * 1000 * 24 * 7))
  }
}

const increaseTime = () => {
  if (timeType.value === 'day') {
    bookTime.value = new Date(bookTime.value.getTime() + 3600 * 1000 * 24)
  } else if (timeType.value === 'week') {
    handleWeekChange(new Date(bookTime.value.getTime() + 3600 * 1000 * 24 * 7))
  }
}

const currentTime = () => {
  if (timeType.value === 'day') {
    bookTime.value = new Date()
  } else if (timeType.value === 'week') {
    handleWeekChange(new Date())
  }
}

const handleWeekChange = (date: Date) => {
  if (!date) return

  // 计算周一的日期（假设周一为周起始日）
  const selectedDate = dayjsR(date)
  const weekStart = selectedDate.startOf('isoWeek') // 兼容不同locale设置
  const weekEnd = selectedDate.endOf('isoWeek')
  // 更新显示值
  startDate.value = weekStart.format('YYYY-MM-DD')
  endDate.value = weekEnd.format('YYYY-MM-DD')
  bookTime.value = weekStart.toDate()
  for (let i = 0; i < 7; i++) {
    weekDate.value[i] = weekStart.add(i, 'day').format('YYYY-MM-DD')
  }
}
const showBookedData = ():Promise<boolean> => {
  return new Promise((resolve) => {
    bookedLoading.value = true
    // 根据当前显示的是日视图还是周视图来调用相应的子组件方法
    if (timeType.value === 'day' && dayTableRef.value) {
      dayTableRef.value.handlePrefixClick(true)
    } else if (timeType.value === 'week' && weekTableRef.value) {
      weekTableRef.value.handlePrefixClick(true)
    }
    resolve(true)
  })
}
const handleBookedChange = (loading:boolean) => {
  bookedLoading.value = loading
}
</script>

<style>
.booking-header-date-picker td, .booking-header-date-picker table {
  border: none
}
</style>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
.today-button {
  height: 25px;
  width: 50px;
}

.book-instruments {
  :deep(.el-dialog__header) {
    padding: 10px 10px 0 10px;
  }

  :deep(.el-dialog__body) {
    padding: 0;
  }

  :deep(.el-row) {
    padding: 0 25px;
    justify-content: space-between;
  }

  :deep(.el-radio-button__inner) {
    height: 22px;
    padding: 4px 18px;
  }

  :deep(.el-radio-group) {
    padding: 2px;
    border: 1.5px solid #02091021;
    border-radius: 5px;
    width: 108px;
    height: 28px;
  }
}

.time-type {
  display: flex;
  align-items: center; /* 垂直居中 */
}

.book-instruments .el-radio-button.is-active :deep(.el-radio-button__inner) {
  background: #F4F3FF; /* 浅蓝色底色 */
  color: #7366FF;
  box-shadow: none; /* 去除默认阴影 */
}

.book-instruments :deep(.el-radio-button__inner) {
  background: white; /* 浅灰色背景 */
  color: #9C9CA6; /* 深蓝色文字 */
  border: none; /* 隐藏边框 */
}

.custom-date-picker {
  margin: 0 20px;
  padding-top: 3px;

  :deep(.el-input__wrapper) {
    box-shadow: none !important; /* 移除输入框阴影边框[5](@ref) */
    padding: 0 0 0 10px;
    width: 130px;
    cursor: pointer !important;
  }

  :deep(.el-picker-panel) {
    border: 0; /* 弹出面板去边框[4](@ref) */
  }

  :deep(.el-input) {
    width: 130px;
    cursor: pointer !important;
  }

  :deep(.el-input__inner) {
    width: 130px;
    cursor: pointer !important;
    font-weight: 500;
    color: #303033;
  }

  :deep(.el-input__prefix) {
    font-weight: 500;
    color: #303033;
  }

  .custom-week-picker {
    :deep(.el-input__wrapper) {
      width: 200px;
    }

    :deep(.el-input) {
      width: 200px;
    }

    :deep(.el-input__inner) {
      width: 200px;
    }
  }
}

.quickTimePick {
  width: 100px;
  justify-content: space-around;
}

:deep(input[readonly], input[readonly]:focus ) {
  padding: 0;
}

.my-book-header {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  justify-content: flex-start; /* 默认左对齐 */
  position: relative; /* 为标题绝对定位提供参照 */
}

.book-title {
  position: absolute; /* 脱离文档流 */
  left: 50%; /* 水平居中定位 */
  transform: translateX(-50%); /* 精确居中 */
  font-size: 14px;
  font-weight: 500;
}

.booked-instruments {
  width: 150px;
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: space-between;
  margin-left: 10px;
}

.header-tail {
  width: 280px;
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: space-between;
  margin-left: 10px;
}

.booked-instruments .el-switch {
  margin-top: 2.5px;
}

.create-book {
  width: 100px;
  font-size: 14px;
}

.left-header {
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

</style>
