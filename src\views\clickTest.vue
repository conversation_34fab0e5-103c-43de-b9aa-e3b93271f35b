<template>
  <el-table
    :data="tableData"
    v-cell-selection
    @selection-change="handleSelection"
    style="width: 100%"
    border
  >
    <el-table-column prop="name" label="姓名"></el-table-column>
    <el-table-column prop="age" label="年龄"></el-table-column>
    <el-table-column prop="address" label="地址"></el-table-column>
    <el-table-column prop="name2" label="姓名"></el-table-column>
    <el-table-column prop="age2" label="年龄"></el-table-column>
    <el-table-column prop="address2" label="地址"></el-table-column>
    <el-table-column prop="name" label="姓名"></el-table-column>
    <el-table-column prop="age" label="年龄"></el-table-column>
    <el-table-column prop="address" label="地址"></el-table-column>
    <el-table-column prop="name2" label="姓名"></el-table-column>
    <el-table-column prop="age2" label="年龄"></el-table-column>
    <el-table-column prop="address2" label="地址"></el-table-column>
  </el-table>
</template>

<script setup>
import { ref } from 'vue'

const tableData = ref([{}, {}, { name: 'Test1' }, {}, { name2: 'test2' }, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}])
const selectedCells = ref(new Set())

const handleSelection = (cells) => {
  console.log('选中单元格:', [...cells])
}

// 自定义指令
const vCellSelection = {
  mounted (el) {
    let isDragging = false
    let startCell = null
    let startPosition = null

    el.addEventListener('mousedown', (e) => {
      document.querySelectorAll('.el-table__cell').forEach(cell => {
        cell.classList.toggle('selected-cell', false)
      })
      selectedCells.value.clear()
      const cell = e.target.closest('.el-table__cell')
      if (cell) {
        startPosition = { x: e.clientX, y: e.clientY }
        isDragging = true
        startCell = getCellPosition(cell)
        el.addEventListener('mousemove', handleMouseMove)
        el.addEventListener('mouseup', handleRelease)
        e.preventDefault()
      } else {
        console.log(cell)
      }
    })

    const handleMouseMove = (e) => {
      const deltaX = Math.abs(e.clientX - startPosition.x)
      const deltaY = Math.abs(e.clientY - startPosition.y)
      if (deltaX < 5 && deltaY < 5) return
      const currentCell = document.elementFromPoint(e.clientX, e.clientY)
      if (currentCell.closest('tr')) {
        const endCell = getCellPosition(currentCell)
        if (startCell && endCell) {
          const cellsInRange = calculateRange(startCell, endCell)
          console.log(cellsInRange)
          highlightCells(cellsInRange)
        }
      }
      e.stopPropagation()
    }
    const handleRelease = () => {
      isDragging = false
      startCell = null
      startPosition = null
      // 解绑事件
      el.removeEventListener('mousemove', handleMouseMove)
      el.removeEventListener('mouseup', handleRelease)
    }
  }
}

// 获取单元格坐标
const getCellPosition = (cell) => {
  const rowIndex = cell.closest('tr').rowIndex
  const colIndex = Array.from(cell.parentNode.children).indexOf(cell)
  return { rowIndex, colIndex }
}

// 计算选中范围
const calculateRange = (start, end) => {
  const row = start.rowIndex
  const minCol = Math.min(start.colIndex, end.colIndex)
  const maxCol = Math.max(start.colIndex, end.colIndex)

  const cells = []
  for (let c = minCol; c <= maxCol; c++) {
    cells.push(`${row}-${c}`)
  }
  return cells
}

// 高亮单元格
const highlightCells = (cells) => {
  document.querySelectorAll('.el-table__row .el-table__cell').forEach(cell => {
    const pos = getCellPosition(cell)
    const isSelected = cells.includes(`${pos.rowIndex}-${pos.colIndex}`)
    cell.classList.toggle('selected-cell', isSelected)
    if (isSelected) selectedCells.value.add(pos)
  })
}
</script>

<style>
.selected-cell {
  background: #7366FF80 !important;
  border: 1px solid #409eff80;
}
:deep(.el-table__cell){
  height: 25px;
}

</style>
