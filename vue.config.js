// vue.config.js
const { defineConfig } = require('@vue/cli-service')
const { WebpackManifestPlugin } = require('webpack-manifest-plugin')

module.exports = defineConfig({
  // 基础配置
  transpileDependencies: true, // 默认转译 node_modules 中的依赖
  productionSourceMap: false, // 生产环境关闭 SourceMap（加速构建）
  outputDir: '../../integle_ineln/frontend/web/bookingInstruments/vue-dist',
  publicPath: '/bookingInstruments/vue-dist/', // 静态资源访问路径
  filenameHashing: true,
  // 自定义 Webpack 配置
  configureWebpack: {
    // 可以在此处添加或覆盖 Webpack 配置
    // 例如插件、别名（alias）、优化配置等
    output: {
      filename: 'js/[name].[contenthash:8].js',
      chunkFilename: 'js/[name].[contenthash:8].chunk.js'
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 500000,
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    },
    plugins: [
      new WebpackManifestPlugin({
        fileName: 'asset-manifest.json',
        publicPath: '/bookingInstruments/vue-dist'
      })
    ]
  },
  css: {
    extract: {
      filename: 'css/[name].[contenthash:8].css',
      chunkFilename: 'css/[name].[contenthash:8].chunk.css'
    }
  },
  // 链式配置 Webpack（推荐，更灵活）
  chainWebpack: (config) => {
    // 修改 TypeScript 加载规则（可选）
    config.module
      .rule('ts')
      .use('ts-loader')
      .loader('ts-loader')
      .tap((options) => ({
        ...options,
        appendTsSuffixTo: [/\.vue$/] // 为 Vue 单文件组件添加 .ts 后缀解析
      }))
  }
})
